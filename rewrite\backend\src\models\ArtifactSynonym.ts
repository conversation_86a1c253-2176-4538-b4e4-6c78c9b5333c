const mongoose = require("mongoose");
const db = require("../modules/db");

const ArtifactSynonymSchema = new mongoose.Schema({
    type: { type: String, required: true },
    word: { type: String, required: true },
    synonyms: { type: [String], required: true },
});

const ArtifactSynonym = db.qm.model("ArtifactSynonym", ArtifactSynonymSchema, "artifact_synonyms");

module.exports = ArtifactSynonym;
