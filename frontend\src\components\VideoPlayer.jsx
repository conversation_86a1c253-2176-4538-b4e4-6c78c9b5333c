import { <PERSON>, <PERSON>rid, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@mui/material";
import React, { forwardRef, useEffect, useRef, useState } from "react";
import { ExitToApp, Fullscreen, Pause, PlayArrow } from "@mui/icons-material";
import { useApp } from "../hooks/AppHook";

const VideoPlayer = forwardRef(({ src, onFullscreen, isFullscreen = false, styles = {} }, parentRef) => {
    const videoRef = useRef(null);
    const { screenSize } = useApp();
    const [isPlaying, setIsPlaying] = useState(false);
    const [currentTime, setCurrentTime] = useState(0);
    const [duration, setDuration] = useState(0);
    const defaultStyle = {
        width: "100%",
        height: isFullscreen ? "100%" : "auto",
        maxHeight: isFullscreen ? "auto" : screenSize.sm || screenSize.md ? "110px" : "100px",
        objectFit: isFullscreen ? "contain" : "cover",
    };

    const setRefs = (element) => {
        // Assign to internal ref
        videoRef.current = element;

        // Assign to parent's forwarded ref

        if (typeof parentRef === "function") {
            parentRef(element);
        } else if (parentRef && typeof parentRef === "object") {
            // Ensure it's a mutable ref object (like one from useRef)
            parentRef.current = element;
        }
    };

    useEffect(() => {
        const video = videoRef.current;
        if (video) {
            const updateTime = () => {
                setCurrentTime(video.currentTime);
                setDuration(video.duration);
            };
            video.addEventListener("timeupdate", updateTime);
            return () => {
                video.removeEventListener("timeupdate", updateTime);
            };
        }
    }, []);

    const handlePlayPause = () => {
        const video = videoRef.current;
        if (video) {
            if (isPlaying) {
                video.pause();
            } else {
                video.play();
            }
            setIsPlaying(!isPlaying);
        }
    };

    const handleSeek = (event, newValue) => {
        const video = videoRef.current;
        if (video) {
            video.currentTime = (newValue / 100) * video.duration;
        }
    };

    const formatTime = (value) => {
        if (value === undefined || value === null || value < 0) {
            return "00:00";
        }
        const totalSeconds = Math.floor((value / 100) * duration);
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;
        return `${String(minutes).padStart(2, "0")}:${String(seconds).padStart(2, "0")}`;
    };
    return (
        <Box
            sx={{
                background: "#000000",
                borderRadius: isFullscreen ? 0 : "10px",
                overflow: "hidden",
                position: "relative",
                height: isFullscreen ? "100%" : "auto",
            }}
            onClick={() => {
                handlePlayPause();
            }}
        >
            <video ref={setRefs} src={src} preload="auto" style={{ ...defaultStyle, ...styles }} />
            <Grid
                container
                alignItems="center"
                gap={1}
                sx={{
                    position: isFullscreen ? "absolute" : "static",
                    bottom: 0,
                    width: "100%",
                    background: "rgba(0, 0, 0, 0.5)",
                    paddingX: 1,
                    paddingBottom: 0.5,
                    paddingTop: isFullscreen ? 0.5 : 0,
                    marginTop: -0.5,
                }}
            >
                <Grid display={"flex"} justifyContent={"center"} alignItems={"center"}>
                    <IconButton onClick={handlePlayPause} sx={{ color: "white", padding: 0 }}>
                        {isPlaying ? <Pause sx={{ fontSize: isFullscreen ? 25 : 15 }} /> : <PlayArrow sx={{ fontSize: isFullscreen ? 25 : 15 }} />}
                    </IconButton>
                </Grid>
                <Grid display={"flex"} justifyContent={"center"} alignItems={"center"} size="grow">
                    <Slider
                        value={duration > 0 ? (currentTime / duration) * 100 : 0}
                        onChange={handleSeek}
                        valueLabelDisplay="auto"
                        valueLabelFormat={(value) => formatTime(value)}
                        sx={{
                            "&.MuiSlider-root": { height: 2 },
                            "& .MuiSlider-thumb": { height: 10, width: 10 },
                            "& .MuiSlider-thumb::after": { content: "none" },
                        }}
                    />
                </Grid>
                <Grid display={"flex"} justifyContent={"center"} alignItems={"center"}>
                    <IconButton onClick={onFullscreen} sx={{ color: "white", padding: 0 }}>
                        {isFullscreen ? (
                            <ExitToApp sx={{ fontSize: isFullscreen ? 25 : 15 }} />
                        ) : (
                            <Fullscreen sx={{ fontSize: isFullscreen ? 25 : 15 }} />
                        )}
                    </IconButton>
                </Grid>
            </Grid>
        </Box>
    );
});

VideoPlayer.displayName = "VideoPlayer";
export default VideoPlayer;
