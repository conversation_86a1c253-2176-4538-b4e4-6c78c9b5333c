import mongoose from 'mongoose';
import User from './User';
import db from '../modules/db';

const TourGuideSchema = new mongoose.Schema({
    user_id: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
    maps: { type: mongoose.Schema.Types.Boolean, default: false },
    streams: { type: mongoose.Schema.Types.Boolean, default: false },
    events: { type: mongoose.Schema.Types.Boolean, default: false },
    notifications: { type: mongoose.Schema.Types.Boolean, default: false },
});

const TourGuide = db.qm.model('TourGuide', TourGuideSchema);

export default TourGuide;