import mongoose from 'mongoose';
import db from '../modules/db';

const GeolocationSchema = new mongoose.Schema({
    location: {
        type: {
            type: String,
            enum: ['Point'],
            default: 'Point'
        },
        coordinates: {
            type: [Number],
            required: true
        }
    },
    name: {
        type: String,
        required: true
    }
});

GeolocationSchema.index({ location: '2dsphere' });

const Geolocation = db.qm.model('Geolocation', GeolocationSchema, 'geolocations');

export default Geolocation;