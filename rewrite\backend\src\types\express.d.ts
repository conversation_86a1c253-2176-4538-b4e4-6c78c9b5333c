import { IApi<PERSON>ey } from "src/interfaces/ApiKey";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, IUser } from "src/interfaces/User";

declare global {
    namespace Express {
        interface Request {
            user: IAuthUser;
            _endpoint_id: number;
            api_key: IApiKey;
        }
    }
}

// declare module 'express-serve-static-core' {
//     interface Request {
//         user?: IUser;
//     }
//     interface Response {
//       myField?: string
//     }
//   }