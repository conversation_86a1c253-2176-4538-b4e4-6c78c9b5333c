import "dotenv/config";
import { google } from 'googleapis';
import axios from 'axios';

// Test script to debug OAuth2 issues
async function testOAuth2() {
    console.log('=== OAuth2 Debug Test ===');
    
    // Check environment variables
    const requiredVars = ['CLIENT_ID', 'CLIENT_SECRET', 'REDIRECT_URI', 'REFRESH_TOKEN', 'MAIL_USER'];
    console.log('\n1. Checking environment variables:');
    
    for (const varName of requiredVars) {
        const value = process.env[varName];
        if (value) {
            console.log(`✓ ${varName}: ${varName === 'CLIENT_SECRET' || varName === 'REFRESH_TOKEN' ? '[HIDDEN]' : value}`);
        } else {
            console.log(`✗ ${varName}: MISSING`);
        }
    }
    
    // Test OAuth2 client creation
    console.log('\n2. Creating OAuth2 client...');
    try {
        const oAuth2Client = new google.auth.OAuth2(
            process.env.CLIENT_ID,
            process.env.CLIENT_SECRET,
            process.env.REDIRECT_URI
        );
        console.log('✓ OAuth2 client created successfully');
        
        // Set credentials
        console.log('\n3. Setting credentials...');
        oAuth2Client.setCredentials({ refresh_token: process.env.REFRESH_TOKEN });
        console.log('✓ Credentials set successfully');
        
        // Test access token retrieval with timeout
        console.log('\n4. Testing access token retrieval...');
        const startTime = Date.now();
        
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => {
                reject(new Error('getAccessToken timed out after 30 seconds'));
            }, 30000);
        });
        
        try {
            const tokenPromise = oAuth2Client.getAccessToken();
            const result = await Promise.race([tokenPromise, timeoutPromise]);
            const endTime = Date.now();
            
            console.log(`✓ Access token retrieved in ${endTime - startTime}ms`);
            console.log('Token result:', {
                hasToken: !!(result as any)?.token,
                tokenLength: (result as any)?.token?.length || 0,
                tokenPrefix: (result as any)?.token?.substring(0, 10) + '...'
            });
            
        } catch (error: any) {
            const endTime = Date.now();
            console.log(`✗ Failed to get access token after ${endTime - startTime}ms`);
            console.error('Error details:', {
                message: error.message,
                code: error.code,
                status: error.status,
                response: error.response?.data
            });
            
            // Provide troubleshooting suggestions
            console.log('\n=== Troubleshooting Suggestions ===');
            if (error.message?.includes('invalid_grant')) {
                console.log('- The refresh token has expired or is invalid');
                console.log('- Generate a new refresh token using Google OAuth2 Playground');
                console.log('- Make sure the refresh token has the correct scopes (Gmail API)');
            } else if (error.message?.includes('unauthorized_client')) {
                console.log('- Check that CLIENT_ID and CLIENT_SECRET are correct');
                console.log('- Verify that the OAuth2 application is properly configured in Google Cloud Console');
            } else if (error.message?.includes('timed out')) {
                console.log('- Network connectivity issues');
                console.log('- Google OAuth2 service may be experiencing issues');
                console.log('- Check firewall/proxy settings');
            } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
                console.log('- Network connectivity issues');
                console.log('- DNS resolution problems');
                console.log('- Check internet connection');
            }
        }

    } catch (error: any) {
        console.log('✗ Failed to create OAuth2 client');
        console.error('Error:', error.message);
    }

    // Test direct HTTP request to OAuth2 endpoint
    console.log('\n5. Testing direct HTTP request to OAuth2 endpoint...');
    const httpStartTime = Date.now();
    try {
        const response = await axios.post('https://oauth2.googleapis.com/token', {
            client_id: process.env.CLIENT_ID,
            client_secret: process.env.CLIENT_SECRET,
            refresh_token: process.env.REFRESH_TOKEN,
            grant_type: 'refresh_token'
        }, {
            timeout: 30000,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        });

        const httpEndTime = Date.now();
        console.log(`✓ Direct HTTP request successful in ${httpEndTime - httpStartTime}ms`);
        console.log('Response status:', response.status);
        console.log('Has access_token:', !!response.data.access_token);

    } catch (error: any) {
        const httpEndTime = Date.now();
        console.log(`✗ Direct HTTP request failed after ${httpEndTime - httpStartTime}ms`);
        console.error('HTTP Error details:', {
            message: error.message,
            code: error.code,
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data
        });
    }

    console.log('\n=== Test Complete ===');
}

// Run the test
testOAuth2().catch(console.error);
