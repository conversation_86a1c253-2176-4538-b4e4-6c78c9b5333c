// Simple test for the email module
require('dotenv/config');
require('ts-node/register');

async function testEmail() {
    try {
        const { sendEmail } = require('./src/modules/email.ts');
        
        console.log('Testing email sending...');
        const result = await sendEmail({
            to: '<EMAIL>', // This won't actually send since it's a test email
            subject: 'Test Email',
            html: '<h1>Test Email</h1><p>This is a test email to verify OAuth2 is working.</p>'
        });
        
        console.log('Email test result:', result);
    } catch (error) {
        console.error('Email test failed:', error.message);
    }
}

testEmail();
