import express from 'express';
import { validateError } from '../utils/functions';
import isAuthenticated from '../middlewares/auth';
import rateLimit from 'express-rate-limit';
import hasPermission from '../middlewares/hasPermission';
import { permissions } from '../utils/permissions';
import ApiEndpoint from '../models/ApiEndpoint';

const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 10,
    message: { message: 'Too many requests from this IP' },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use('/', apiLimiter);

router.get('/',
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageApiKeys]),
    async (req, res) => {
        try {
            const apiEndpoints = await ApiEndpoint.find()
            res.json(apiEndpoints);
        } catch (err) {
            validateError(err, res)
        }
    }
);

export default router;