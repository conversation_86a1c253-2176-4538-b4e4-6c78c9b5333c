import express from 'express';
import { validateError } from '../utils/functions';
import db from '../modules/db';
import { validateData } from '../middlewares/validator';
import { query } from 'express-validator';
import rateLimit from 'express-rate-limit';
import assignEndpointId from '../middlewares/assignEndpointId';
import { endpointIds } from '../utils/endpointIds';
import isAuthenticated from '../middlewares/auth';
import hasPermission from '../middlewares/hasPermission';
import { permissions } from '../utils/permissions';
import seaPorts from '../utils/seaPorts';
import Statistics from '../models/Statistics';
import { FilterQuery } from 'mongoose';
import { IStatistics } from 'src/interfaces/Statistics';

const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 60 * 1000,
    limit: 10,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.get(
    "/",
    assignEndpointId.bind(this, endpointIds.FETCH_STATISTICS),
    isAuthenticated,
    hasPermission.bind(this, [permissions.viewStatistics]),
    (req, res, next) =>
        validateData(
            [
                query("type")
                    .isString()
                    .isIn(["daily", "weekly"])
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
            ],
            req,
            res,
            next,
        ),
    async (req, res) => {
        try {
            const { type } = req.query;

            const query: FilterQuery<IStatistics> = {}
            if (type) query.type = type

            const statistics = await Statistics.find(query, {}, { sort: { fromTimestamp: -1 } });

            res.json(statistics);
        } catch (err) {
            validateError(err, res);
        }
    },
);

/**
 * @swagger
 * tags:
 *   - name: Statistics
 *     description: Fetch aggregate statistics
 * components:
 *   schemas:
 *     Statistics:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Document Id of the statistics
 *           example: 67942a74a7f838634a00190a
 *         stats:
 *           type: object
 *           description: Mixed type containing the statistics data
 *           example:
 *             totalVesselsDetected: 34
 *             totalSmartmastsOnline: 300
 *         fromTimestamp:
 *           type: string
 *           format: date-time
 *           description: The starting timestamp for the statistics period
 *           example: "2024-09-30T00:00:00.000Z"
 *         toTimestamp:
 *           type: string
 *           format: date-time
 *           description: The ending timestamp for the statistics period
 *           example: "2024-10-07T00:00:00.000Z"
 *         type:
 *           type: string
 *           enum: [daily, weekly]
 *           description: The type of statistics (daily or weekly)
 *           example: "weekly"
 */

/**
 * @swagger
 * /statistics:
 *   get:
 *     summary: Fetch aggregate statistics
 *     description: Retrieves a list of statistics records, either daily or weekly based on the query parameter.<br/>Rate limited to 10 requests every 60 seconds
 *     tags: [Statistics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [daily, weekly]
 *         description: Type of statistics to fetch (daily or weekly). Optional, if not provided returns all.
 *         required: false
 *     responses:
 *       200:
 *         description: A list of statistics records
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Statistics'
 *       400:
 *         description: Invalid request, usually when the provided type is incorrect.
 *       401:
 *         description: Unauthorized, the user must be authenticated.
 *       403:
 *         description: Forbidden, the user doesn't have permission to view statistics.
 *       500:
 *         description: Internal server error
 */

export default router;