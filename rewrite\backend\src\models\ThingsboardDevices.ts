const mongoose = require("mongoose");
const db = require("../modules/db");

const thingsboardDeviceSchema = new mongoose.Schema(
    {
        deviceId: { type: String, required: true, unique: true },
        dashboardId: { type: String, required: false },
        deviceName: { type: String, required: true },
        accessToken: { type: String, required: true },
    },
    { timestamps: true },
);

// Create a partial index that only applies to non-null dashboardId values
thingsboardDeviceSchema.index(
    { dashboardId: 1 },
    {
        unique: true,
        partialFilterExpression: { dashboardId: { $type: "string" } },
    },
);

const ThingsboardDevices = db.qmShared.model("ThingsboardDevices", thingsboardDeviceSchema, "thingsboard_devices");

module.exports = ThingsboardDevices;
