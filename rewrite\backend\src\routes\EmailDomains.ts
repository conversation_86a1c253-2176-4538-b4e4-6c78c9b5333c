const express = require("express");
const { default: rateLimit } = require("express-rate-limit");
const isAuthenticated = require("../middlewares/auth");
const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const EmailDomains = require("../models/EmailDomains");
const { validateError } = require("../utils/functions");
const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.get("/", assignEndpointId.bind(this, endpointIds.FETCH_ALLOWED_EMAIL_DOMAINS), isAuthenticated, async (req, res) => {
    try {
        const emailDomains = await EmailDomains.find({});
        res.status(200).json(emailDomains);
    } catch (error) {
        validateError(error, res);
    }
});

module.exports = router;
