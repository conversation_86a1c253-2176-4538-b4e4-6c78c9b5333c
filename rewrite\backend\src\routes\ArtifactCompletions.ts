const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const isAuthenticated = require("../middlewares/auth");
const { validateData } = require("../middlewares/validator");
const { body } = require("express-validator");
const db = require("../modules/db");
const UserCompletionLogs = require("../models/UserCompletionLogs");
const { validateError } = require("../utils/functions");
const openai = require("openai");
const { default: rateLimit } = require("express-rate-limit");
const router = require("./S3");
const Vessel = require("../models/Vessel");
const ArtifactSuggestion = require("../models/ArtifactSuggestion");
const ArtifactSynonym = require("../models/ArtifactSynonym");
const SpellingCorrector = require("spelling-corrector");
const { cleanSuggestion } = require("../utils/functions");

const spelling = new SpellingCorrector();
spelling.loadDictionary();

const lastCheckedAt = {
    superCategories: null,
    categories: null,
    sizes: null,
    vessels: null,
    weapons: null,
    synonyms: null,
    prompt: null,
};
const checkPeriodMs = 3600000; // 1 hour in ms
const dataCache = {};

const objectKeys = {
    country: "country_flags",
    color: "colors",
    size: "sizes",
    category: "categories",
    subcategory: "subcategories",
};
const openaiClient = new openai.OpenAI(process.env.OPENAI_API_KEY);
const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 5,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

const parseNLPResponse = (responseContent) => {
    const parsedResponse = JSON.parse(responseContent.replace(/```json|```|\n|\/|\\/g, ""));

    for (const key in objectKeys) {
        if (parsedResponse[key]) {
            parsedResponse[objectKeys[key]] = parsedResponse[key];
            delete parsedResponse[key];
        }
    }
    return parsedResponse;
};

const getSuperCategoriesList = async () => {
    if (!lastCheckedAt.superCategories || !dataCache.superCategories || Date.now() - lastCheckedAt.superCategories.getTime() >= checkPeriodMs) {
        dataCache.superCategories = await db.qmai.collection("analysis_results").distinct("super_category", { super_category: { $ne: null } });
        lastCheckedAt.superCategories = new Date();
    }
    return dataCache.superCategories;
};

const getCategoriesList = async () => {
    if (!lastCheckedAt.categories || !dataCache.categories || Date.now() - lastCheckedAt.categories.getTime() >= checkPeriodMs) {
        dataCache.categories = await db.qmai.collection("analysis_results").distinct("category", { category: { $ne: null } });
        lastCheckedAt.categories = new Date();
    }
    return dataCache.categories;
};

const getSizesList = async () => {
    if (!lastCheckedAt.sizes || !dataCache.sizes || Date.now() - lastCheckedAt.sizes.getTime() >= checkPeriodMs) {
        dataCache.sizes = await db.qmai.collection("analysis_results").distinct("size", { size: { $ne: null } });
        lastCheckedAt.sizes = new Date();
    }
    return dataCache.sizes;
};

const getVesselList = async () => {
    if (!lastCheckedAt.vessels || !dataCache.vessels || Date.now() - lastCheckedAt.vessels.getTime() >= checkPeriodMs) {
        const vessels = await Vessel.find({}, { name: 1 });
        dataCache.vessels = vessels.map((v) => v.name);
        lastCheckedAt.vessels = new Date();
    }
    return dataCache.vessels;
};

const getWeaponsList = async () => {
    if (!lastCheckedAt.weapons || !dataCache.weapons || Date.now() - lastCheckedAt.weapons.getTime() >= checkPeriodMs) {
        const weaponsAgg = await db.qmai
            .collection("analysis_results")
            .aggregate([
                { $match: { weapons: { $ne: null } } },
                { $group: { _id: "$weapons", count: { $sum: 1 } } },
                { $match: { count: { $gt: 10 } } },
                { $sort: { count: -1 } },
                { $project: { _id: 0, name: "$_id", count: 1 } },
            ])
            .toArray();
        dataCache.weapons = weaponsAgg.map((w) => w.name);
        lastCheckedAt.weapons = new Date();
    }
    return dataCache.weapons;
};

const getSynonymsList = async () => {
    if (!lastCheckedAt.synonyms || !dataCache.synonyms || Date.now() - lastCheckedAt.synonyms.getTime() >= checkPeriodMs) {
        dataCache.synonyms = await ArtifactSynonym.find({}, { _id: 0 });
        lastCheckedAt.synonyms = new Date();
    }
    return dataCache.synonyms;
};

const artifactCompletionsPrompt = async () => {
    if (lastCheckedAt.prompt && dataCache.prompt && Date.now() - lastCheckedAt.prompt.getTime() < checkPeriodMs) {
        return dataCache.prompt;
    }

    const [superCategories, categories, sizes, vessels, weapons, allSynonyms] = await Promise.all([
        getSuperCategoriesList(),
        getCategoriesList(),
        getSizesList(),
        getVesselList(),
        getWeaponsList(),
        getSynonymsList(),
    ]);

    if (lastCheckedAt.prompt && !dataCache.prompt && Date.now() - lastCheckedAt.prompt.getTime() < checkPeriodMs) {
        return dataCache.prompt;
    }

    const synonymMap = {};
    for (const syn of allSynonyms) {
        if (!synonymMap[syn.type]) synonymMap[syn.type] = [];
        synonymMap[syn.type].push(`${syn.word}: ${syn.synonyms.join(", ")}`);
    }

    const synonymInstructions = Object.entries(synonymMap)
        .map(([type, entries]) => `${type} synonyms:\n  - ${entries.join("\n  - ")}`)
        .join("\n\n");

    const refTime = new Date().toISOString();
    const systemMessage = `
            Use the following synonym mappings to interpret user intent. Do not return them. Just use them for better normalization.
            ${synonymInstructions}

            Following are some JSON Rules:
            1. **Type Detection**
            - If a type ("image", "video", or "both") is detected in user input, return it as the \`type\` field.
            - If no type is detected, default to: \`type = "both"\`.

            2. **Time Extraction**
            - Use this as the current reference time: \`${refTime}\`.
            - Detect both specific and relative time expressions in the user input.
            - Return all time values in **ISO 8601 UTC** format.
            - Time field usage:
                - **Specific Instant** (e.g., "3 PM Feb 18 2024") → return as \`time\`.
                - **Relative or Period-based Phrases** (e.g., "Feb 18", "this month", "yesterday") → return as:
                - \`start_time\`: beginning of the period
                - \`end_time\`: end of the period
            - Examples (assuming current reference time is \`${refTime}\`):
                - "this month" → 
                \`start_time: "YYYY-MM-01T00:00:00.000Z"\`  
                \`end_time: "YYYY-MM-31T23:59:59.999Z"\`
                - "last month" → full range of the previous calendar month
                - "Feb 18" (without year) → use the current year from reference time  
                \`start_time: "YYYY-02-18T00:00:00.000Z"\`  
                \`end_time: "YYYY-02-18T23:59:59.999Z"\`
            - If no time is detected, omit all time-related fields.
            3. **Response Format**
            - Always call the tool \`extract_vessel_metadata\` with a JSON object in the \`arguments\` field.
            - Do **not** respond with free-form text.   
            - The \`arguments\` must always be a **valid JSON object** matching the tool schema.
            - If no relevant metadata is found, return an **empty object** (\`{}\`) via the tool call.
            - Never respond with explanations, summaries, or notes — just call the tool.
            `;

    const toolSchema = {
        tool: {
            type: "function",
            function: {
                name: "extract_vessel_metadata",
                description: "Extract metadata from a vessel-related user search input.",
                parameters: {
                    type: "object",
                    properties: {
                        country_flags: { type: "array", items: { type: "string" } },
                        colors: { type: "array", items: { type: "string" } },
                        categories: { type: "array", items: { type: "string", enum: superCategories } },
                        subcategory: { type: "array", items: { type: "string", enum: categories } },
                        sizes: { type: "array", items: { type: "string", enum: sizes } },
                        weapons: { type: "array", items: { type: "string", enum: weapons } },
                        vessel_name: { type: "array", items: { type: "string", enum: vessels } },
                    },
                    required: [],
                },
            },
        },
        systemMessage: systemMessage,
    };

    dataCache.prompt = toolSchema;
    lastCheckedAt.prompt = new Date();
    return toolSchema;
};

router.use("/", apiLimiter);

router.post(
    "/",
    assignEndpointId.bind(this, endpointIds.FETCH_NLP_SUGGESTIONS),
    isAuthenticated,
    (req, res, next) => validateData([body("text").isString().notEmpty().withMessage("Text needs to be string and not empty")], req, res, next),
    async (req, res) => {
        try {
            // Step 1: Extract user text and build prompt
            const { text } = req.body;
            const { tool, systemMessage } = await artifactCompletionsPrompt();

            // Step 2: Call OpenAI for completions
            const completion = await openaiClient.chat.completions.create({
                model: "gpt-4o",
                messages: [
                    { role: "system", content: systemMessage },
                    { role: "user", content: text },
                ],
                tools: [tool],
                tool_choice: {
                    type: "function",
                    function: { name: "extract_vessel_metadata" },
                },
            });

            // Step 3: Parse and validate response
            const choice = completion.choices[0]?.message;
            const functionCall = choice?.tool_calls?.[0]?.function;
            const raw = functionCall?.arguments;

            if (!raw) {
                return res.status(400).json({
                    message: "Your search does not match any known artifacts.",
                });
            }

            // Step 4: Log completion analytics
            UserCompletionLogs.create({
                user_id: req.user._id || null,
                command: text,
                response: raw,
                completion_type: "events_filter",
            });

            const parsedResponse = parseNLPResponse(raw);

            // Step 5: Vessel name to ID mapping
            let vessel_ids = [];
            const names = parsedResponse?.vessel_name;
            if (names && Array.isArray(names)) {
                const vessels = await Vessel.find({ name: { $in: names } });
                vessel_ids = vessels.map((v) => v._id);
            } else if (names) {
                const vessels = await Vessel.find({ name: new RegExp(`^${names}$`, "i") });
                vessel_ids = vessels.map((v) => v._id);
            }
            parsedResponse.vessel_ids = vessel_ids.length > 0 ? vessel_ids : null;
            delete parsedResponse.vessel_name;

            // Step 6: Ensure type and normalize arrays
            if (!parsedResponse.type || typeof parsedResponse.type !== "string") parsedResponse.type = "both";
            const arrayFields = ["country_flags", "vessel_ids", "categories", "colors", "sizes", "weapons"];
            arrayFields.forEach((k) => {
                if (!Array.isArray(parsedResponse[k])) {
                    parsedResponse[k] = parsedResponse[k] ? [parsedResponse[k]] : null;
                }
            });

            // Step 7: Handle time fields
            if (parsedResponse.time && !parsedResponse.start_time && !parsedResponse.end_time) {
                parsedResponse.start_time = parsedResponse.end_time = parsedResponse.time;
                delete parsedResponse.time;
            }
            ["start_time", "end_time"].forEach((t) => {
                if (parsedResponse[t] && isNaN(Date.parse(parsedResponse[t]))) {
                    parsedResponse[t] = null;
                }
            });

            // Step 8: Filter only allowed keys
            const allowedKeys = ["type", "country_flags", "vessel_ids", "categories", "start_time", "end_time", "colors", "sizes", "weapons"];
            const filtered = Object.fromEntries(Object.entries(parsedResponse).filter(([k]) => allowedKeys.includes(k)));

            // Step 9: Store or update suggestion click count (normalized/corrected, always lowercase)
            if (text) {
                let cleanText = cleanSuggestion(text).toLowerCase();
                if (cleanText && !cleanText.includes(" ")) {
                    cleanText = spelling.correct(cleanText);
                }
                const existing = await ArtifactSuggestion.findOne({ search: cleanText });
                if (existing) {
                    existing.click = (existing.click || 0) + 1;
                    await existing.save();
                } else {
                    await ArtifactSuggestion.create({ search: cleanText, click: 1, impressions: 0 });
                }
            }

            res.json(filtered);
        } catch (err) {
            validateError(err, res);
        }
    },
);

module.exports = router;
