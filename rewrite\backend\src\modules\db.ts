import mongoose from 'mongoose';

mongoose.set("strictQuery", false);

if (!process.env.MONGO_URI) {
    throw new Error("MONGO_URI must be set in env variables");
}

const db = {
    qm: mongoose.createConnection(process.env.MONGO_URI, { dbName: "quartermaster-dev" }),
    qmai: mongoose.createConnection(process.env.MONGO_URI, { dbName: "artifact_processor" }),
    qmShared: mongoose.createConnection(process.env.MONGO_URI, { dbName: "quartermaster-shared" }),
    locations: mongoose.createConnection(process.env.MONGO_URI, { dbName: "locations" }),
    locationsOptimized: mongoose.createConnection(process.env.MONGO_URI, { dbName: "locations_optimized" }),
    locationsRaw: mongoose.createConnection(process.env.MONGO_URI, { dbName: "locations_raw" }),
    ais: mongoose.createConnection(process.env.MONGO_URI, { dbName: "ais" }),
    audio: mongoose.createConnection(process.env.MONGO_URI, { dbName: "audio_processor" }),
    lookups: mongoose.createConnection(process.env.MONGO_URI, { dbName: "lookups" }),
};

db.qm.on("open", () => console.log("DB connected to Quartermaster"));
db.qmai.on("open", () => console.log("DB connected to QMAI"));
db.qmShared.on("open", () => console.log("DB connected to Quartermaster-Shared"));
db.locations.on("open", () => console.log("DB connected to Locations"));
db.locationsOptimized.on("open", () => console.log("DB connected to Locations Optimized"));
db.locationsRaw.on("open", () => console.log("DB connected to Locations Raw"));
db.ais.on("open", () => console.log("DB connected to AIS"));
db.audio.on("open", () => console.log("DB connected to QMAudio"));
db.lookups.on("open", () => console.log("DB connected to Lookups"));

db.qm.on("error", (err) => console.error(err));
db.qmai.on("error", (err) => console.error(err));
db.qmShared.on("error", (err) => console.error(err));
db.locations.on("error", (err) => console.error(err));
db.locationsOptimized.on("error", (err) => console.error(err));
db.locationsRaw.on("error", (err) => console.error(err));
db.ais.on("error", (err) => console.error(err));
db.audio.on("error", (err) => console.error(err));
db.lookups.on("error", (err) => console.error(err));

export default db