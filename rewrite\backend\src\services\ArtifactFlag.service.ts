const mongoose = require("mongoose");
const ArtifactFlag = require("../models/ArtifactFlag");
const db = require("../modules/db");

class ArtifactFlagService {
    async flagArtifact(artifactId, userId) {
        const artifact = await db.qmai.collection("analysis_results").findOne({
            _id: mongoose.Types.ObjectId(artifactId),
        });
        if (!artifact) {
            throw new Error("Artifact not found");
        }

        const existingFlag = await ArtifactFlag.findOne({
            artifactId: mongoose.Types.ObjectId(artifactId),
            flaggedBy: mongoose.Types.ObjectId(userId),
        });
        if (existingFlag) {
            throw new Error("You have already flagged this artifact");
        }

        const flag = new ArtifactFlag({
            artifactId: mongoose.Types.ObjectId(artifactId),
            flaggedBy: mongoose.Types.ObjectId(userId),
        });

        await flag.save();
        return flag;
    }

    async getFlaggedArtifacts() {
        const flaggedArtifacts = await ArtifactFlag.aggregate([
            {
                $lookup: {
                    from: "users",
                    let: { userId: "$flaggedBy" },
                    pipeline: [{ $match: { $expr: { $eq: ["$_id", "$$userId"] } } }, { $project: { _id: 1, name: 1, email: 1 } }],
                    as: "flaggedByUser",
                },
            },
            {
                $group: {
                    _id: "$artifactId",
                    flags: {
                        $push: {
                            _id: "$_id",
                            flaggedBy: "$flaggedBy",
                            flaggedByUser: { $arrayElemAt: ["$flaggedByUser", 0] },
                            flaggedAt: "$flaggedAt",
                        },
                    },
                    flagCount: { $sum: 1 },
                    latestFlagDate: { $max: "$flaggedAt" },
                },
            },
            { $sort: { latestFlagDate: -1 } },
        ]);

        const artifactIds = flaggedArtifacts.map((item) => item._id);
        if (artifactIds.length === 0) {
            return [];
        }

        const artifacts = await db.qmai
            .collection("analysis_results")
            .find(
                { _id: { $in: artifactIds } },
                {
                    projection: {
                        _id: 1,
                        unit_id: 1,
                        bucket_name: 1,
                        image_path: 1,
                        video_path: 1,
                        thumbnail_image_path: 1,
                        location: 1,
                        category: 1,
                        super_category: 1,
                        size: 1,
                        color: 1,
                        weapons: 1,
                        others: 1,
                        timestamp: 1,
                        created_at: 1,
                        onboard_vessel_name: 1,
                        onboard_vessel_id: 1,
                        country_flag: 1,
                        aws_region: 1,
                        text_extraction: 1,
                        imo_number: 1,
                        is_archived: 1,
                        archived_by: 1,
                    },
                },
            )
            .toArray();

        const results = flaggedArtifacts
            .map((flagData) => {
                const artifact = artifacts.find((a) => a._id.toString() === flagData._id.toString());
                return {
                    artifactId: artifact._id,
                    artifact: artifact,
                    flags: flagData.flags.map((flag) => ({
                        ...flag,
                        user: flag.flaggedByUser,
                        created_at: flag.flaggedAt,
                    })),
                    flagCount: flagData.flagCount,
                    latestFlagDate: flagData.latestFlagDate,
                };
            })
            .filter((result) => result.artifact && result.artifact.is_archived !== true);

        return results;
    }

    async unflagArtifact(artifactId, userId) {
        const flag = await ArtifactFlag.findOneAndDelete({
            artifactId: mongoose.Types.ObjectId(artifactId),
            flaggedBy: mongoose.Types.ObjectId(userId),
        });
        if (!flag) {
            throw new Error("Flag not found");
        }
        return flag;
    }

    async getUserFlaggedArtifactIds(userId) {
        const flags = await ArtifactFlag.find({ flaggedBy: mongoose.Types.ObjectId(userId) }, { artifactId: 1, _id: 0 });
        return flags.map((flag) => flag.artifactId.toString());
    }

    async removeAllFlagsFromArtifact(artifactId) {
        const result = await ArtifactFlag.deleteMany({
            artifactId: mongoose.Types.ObjectId(artifactId),
        });
        return result;
    }
}

module.exports = new ArtifactFlagService();
