import express from "express";
import userRoute from './User'

const router = express.Router();

router.use("/users", userRoute);
// router.use("/kinesis", require("./Kinesis"));
// router.use("/regions", require("./Region"));
// router.use("/vesselLocations", require("./VesselLocation"));
// router.use("/roles", require("./Role"));
// router.use("/permissions", require("./Permission"));
// router.use("/artifacts", require("./Artifact"));
// router.use("/artifactFavourites", require("./ArtifactFavourites"));
// router.use("/suggestions", require("./ArtifactSuggestions"));
// router.use("/logs", require("./Log"));
// router.use("/s3", require("./S3"));
// router.use("/apiKeys", require("./ApiKey"));
// router.use("/apiEndpoints", require("./ApiEndpoint"));
// router.use("/statistics", require("./Statistics"));
// router.use("/vessels", require("./Vessels"));
// router.use("/geolocations", require("./Geolocation"));
// router.use("/regionGroups", require("./RegionGroup"));
// router.use("/tourGuides", require("./TourGuide"));
// router.use("/notificationsAlerts", require("./NotificationAlert"));
// router.use("/inAppNotifications", require("./InAppNotification"));
// router.use("/summaryReports", require("./NotificationSummary"));
// router.use("/completions", require("./ArtifactCompletions"));
// router.use("/emailsDomain", require("./EmailDomains"));
// router.use("/organizations", require("./Organization"));
// router.use("/homePorts", require("./HomePorts"));
// router.use("/vesselManagement", require("./VesselManagement"));
// router.use("/thingsboard", require("./Thingsboard"));
// router.use("/vesselAis", require("./VesselAis.route"));
// router.use("/audios", require("./Audio.route"));

export default router;
