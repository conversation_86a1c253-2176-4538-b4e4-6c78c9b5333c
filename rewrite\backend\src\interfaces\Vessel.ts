import mongoose from "mongoose";

export interface IUnitsHistory {
    unit_id: string;
    mount_timestamp: Date;
    unmount_timestamp: Date | null;
}

export interface IVessel {
    _id: mongoose.Types.ObjectId | string;
    name: string;
    thumbnail_s3_key: string | null;
    unit_id: string | null;
    is_active: boolean;
    units_history: IUnitsHistory[];
    creation_timestamp: Date;
    created_by: mongoose.Types.ObjectId;
}