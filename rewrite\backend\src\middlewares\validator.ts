import { NextFunction, Request, Response } from 'express';
import { Valida<PERSON><PERSON>hai<PERSON>, validationResult } from 'express-validator';

const validateData = async (rules: Validation<PERSON>hain[], req: Request, res: Response, next: NextFunction) => {
    // Run validation rules and gather validation errors
    return Promise.all(rules.map(rule => rule.run(req))).then(() => {
        const errors = validationResult(req);

        // If there are validation errors, respond with a 400 Bad Request status and error messages
        if (errors.isEmpty()) {
            next();
        } else {
            console.log(errors.array())
            return res.status(400).json({ message: errors.array()[0].msg });
        }
    });
};

export {
    validateData,
};