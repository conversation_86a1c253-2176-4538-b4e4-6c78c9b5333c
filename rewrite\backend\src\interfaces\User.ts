import mongoose from "mongoose";
import { IPermission } from "./Permission";
import { IRole } from "./Role";
import { IOrganization } from "./Organization";

export interface IUser {
    _id: mongoose.Types.ObjectId | string;
    name: string;
    email?: string;
    username?: string;
    password: string;
    date_time_format?: string;
    use_MGRS?: boolean;
    jwt_tokens: string[];
    reset_password_token: string | null;
    reset_password_expire: number | null;
    email_verification_enabled: boolean;
    email_verified_device_ids: string[];
    role_id: number;
    deletable: boolean;
    allowed_vessels: string[];
    organization_id: mongoose.Types.ObjectId | string;
    home_port_filter_mode: "ALL" | "ONLY_HOME_PORTS" | "ONLY_NON_HOME_PORTS";
    is_deleted: boolean;
    created_by: mongoose.Types.ObjectId;
    creation_timestamp: Date;
}

export interface IAuthUser {
    _id: mongoose.Types.ObjectId | string;
    name: string;
    email?: string;
    username?: string;
    jwt_tokens: string[];
    email_verification_enabled: boolean;
    email_verified_device_ids: string[];
    role_id: number;
    deletable: boolean;
    is_deleted: boolean;
    permissions: IPermission[];
    allowed_vessels: string[];
    role: IRole;
    created_by: mongoose.Types.ObjectId;
    creation_timestamp: Date;
    organization: IOrganization;
    organization_id: mongoose.Types.ObjectId | string;
}