import express from 'express';
import Region from '../models/Region';
import { validateError } from '../utils/functions';
import isAuthenticated from '../middlewares/auth';
import rateLimit from 'express-rate-limit';
import assignEndpointId from '../middlewares/assignEndpointId';
import { endpointIds } from '../utils/endpointIds';

const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 10,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.get("/", assignEndpointId.bind(this, endpointIds.FETCH_REGIONS), isAuthenticated, async (req, res) => {
    try {
        const regions = await Region.find();
        res.json(regions);
    } catch (err) {
        validateError(err, res);
    }
});

export default router;

/**
 * @swagger
 * tags:
 *   name: Regions
 *   description: Fetch regions
 * components:
 *   schemas:
 *     Region:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Document Id of the region
 *           example: 67942a74a7f838634a00190a
 *         name:
 *           type: number
 *           description: Name of the region
 *           example: US East (Ohio)
 *         value:
 *           type: string
 *           description: Region identifier
 *           example: us-east-2
 *         is_live:
 *           type: boolean
 *           description: Whether this region is in use
 *           example: false
 */

/**
 * @swagger
 * /regions:
 *   get:
 *     summary: Fetch all regions
 *     description: Rate limited to 10 requests every 5 seconds
 *     tags: [Regions]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: A list of regions
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Region'
 *       500:
 *         description: Server error
 */
