import nodemailer from 'nodemailer';
import { google } from 'googleapis';

// Validate required environment variables
const requiredEnvVars = ['CLIENT_ID', 'CLIENT_SECRET', 'REDIRECT_URI', 'REFRESH_TOKEN', 'MAIL_USER'];
const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables for email service: ${missingVars.join(', ')}`);
}

// Create OAuth2 client with timeout configuration
const oAuth2Client = new google.auth.OAuth2(
    process.env.CLIENT_ID,
    process.env.CLIENT_SECRET,
    process.env.REDIRECT_URI
);

// Set credentials with refresh token
oAuth2Client.setCredentials({ refresh_token: process.env.REFRESH_TOKEN });

// Configure timeout for OAuth2 requests (30 seconds)
const OAUTH_TIMEOUT = 30000;

// Helper function to create a timeout promise
function createTimeoutPromise(ms: number, operation: string) {
    return new Promise((_, reject) => {
        setTimeout(() => {
            reject(new Error(`${operation} timed out after ${ms}ms`));
        }, ms);
    });
}

// Helper function to get access token with timeout
async function getAccessTokenWithTimeout(): Promise<string> {
    console.log('Attempting to get OAuth2 access token...');

    try {
        const tokenPromise = oAuth2Client.getAccessToken();
        const timeoutPromise = createTimeoutPromise(OAUTH_TIMEOUT, 'OAuth2 getAccessToken');

        const result = await Promise.race([tokenPromise, timeoutPromise]);

        if (!result || typeof result !== 'object' || !('token' in result)) {
            throw new Error('Invalid response from OAuth2 getAccessToken');
        }

        const { token } = result as { token: string };

        if (!token) {
            throw new Error('No access token received from OAuth2 service');
        }

        console.log('Successfully obtained OAuth2 access token');
        return token;

    } catch (error: any) {
        console.error('OAuth2 getAccessToken error:', {
            message: error.message,
            code: error.code,
            status: error.status,
            stack: error.stack
        });

        // Provide more specific error messages
        if (error.message?.includes('invalid_grant')) {
            throw new Error('OAuth2 refresh token has expired or is invalid. Please regenerate the refresh token.');
        } else if (error.message?.includes('unauthorized_client')) {
            throw new Error('OAuth2 client credentials are invalid. Please check CLIENT_ID and CLIENT_SECRET.');
        } else if (error.message?.includes('timed out')) {
            throw new Error('OAuth2 service is not responding. Please try again later.');
        } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
            throw new Error('Network error: Unable to connect to Google OAuth2 service.');
        }

        throw error;
    }
}

async function sendEmail({ to, subject, html }: { to: string, subject: string, html: string }) {
    return new Promise(async (resolve, reject) => {
        try {
            console.log(`Sending email to: ${to}, subject: ${subject}`);

            // Get access token with timeout protection
            const accessToken = await getAccessTokenWithTimeout();

            const transport = nodemailer.createTransport({
                service: 'gmail',
                auth: {
                    type: 'OAuth2',
                    user: process.env.MAIL_USER,
                    clientId: process.env.CLIENT_ID,
                    clientSecret: process.env.CLIENT_SECRET,
                    refreshToken: process.env.REFRESH_TOKEN,
                    accessToken: accessToken,
                },
                // Add timeout for nodemailer operations
                connectionTimeout: 30000,
                greetingTimeout: 30000,
                socketTimeout: 30000,
            });

            await transport.sendMail({
                to,
                subject,
                html,
            });

            console.log(`Email sent successfully to: ${to}`);
            resolve('Email sent')
        } catch (err: any) {
            console.error(`Error sending email to ${to}:`, {
                message: err.message,
                code: err.code,
                stack: err.stack
            });
            reject(err)
        }
    })
}

export {
    sendEmail
}