import nodemailer from 'nodemailer';
import { google } from 'googleapis';

// Validate required environment variables
const requiredEnvVars = ['CLIENT_ID', 'CLIENT_SECRET', 'REDIRECT_URI', 'REFRESH_TOKEN', 'MAIL_USER'];
const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables for email service: ${missingVars.join(', ')}`);
}

// Create OAuth2 client with timeout configuration
const oAuth2Client = new google.auth.OAuth2(
    process.env.CLIENT_ID,
    process.env.CLIENT_SECRET,
    process.env.REDIRECT_URI
);

// Set credentials with refresh token
oAuth2Client.setCredentials({ refresh_token: process.env.REFRESH_TOKEN });

// Configure timeout for OAuth2 requests (60 seconds for slow networks)
const OAUTH_TIMEOUT = 60000;
const MAX_RETRIES = 3;
const RETRY_DELAY = 2000; // 2 seconds between retries

// Helper function to create a timeout promise
function createTimeoutPromise(ms: number, operation: string) {
    return new Promise((_, reject) => {
        setTimeout(() => {
            reject(new Error(`${operation} timed out after ${ms}ms`));
        }, ms);
    });
}

// Helper function to wait for a specified time
function delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Helper function to get access token with timeout and retry logic
async function getAccessTokenWithTimeout(): Promise<string> {
    let lastError: any;

    for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
        console.log(`Attempting to get OAuth2 access token (attempt ${attempt}/${MAX_RETRIES})...`);

        try {
            // Create a new OAuth2 client for each attempt to avoid any cached state issues
            const freshOAuth2Client = new google.auth.OAuth2(
                process.env.CLIENT_ID,
                process.env.CLIENT_SECRET,
                process.env.REDIRECT_URI
            );
            freshOAuth2Client.setCredentials({ refresh_token: process.env.REFRESH_TOKEN });

            const tokenPromise = freshOAuth2Client.getAccessToken();
            const timeoutPromise = createTimeoutPromise(OAUTH_TIMEOUT, 'OAuth2 getAccessToken');

            const result = await Promise.race([tokenPromise, timeoutPromise]);

            if (!result || typeof result !== 'object' || !('token' in result)) {
                throw new Error('Invalid response from OAuth2 getAccessToken');
            }

            const { token } = result as { token: string };

            if (!token) {
                throw new Error('No access token received from OAuth2 service');
            }

            console.log(`Successfully obtained OAuth2 access token on attempt ${attempt}`);
            return token;

        } catch (error: any) {
            lastError = error;
            console.error(`OAuth2 getAccessToken attempt ${attempt} failed:`, {
                message: error.message,
                code: error.code,
                status: error.status
            });

            // Don't retry for certain types of errors
            if (error.message?.includes('invalid_grant') ||
                error.message?.includes('unauthorized_client') ||
                error.message?.includes('invalid_client')) {
                console.log('Non-retryable error detected, stopping retries');
                break;
            }

            // Wait before retrying (except on last attempt)
            if (attempt < MAX_RETRIES) {
                console.log(`Waiting ${RETRY_DELAY}ms before retry...`);
                await delay(RETRY_DELAY);
            }
        }
    }

    // All attempts failed, provide specific error messages
    if (lastError.message?.includes('invalid_grant')) {
        throw new Error('OAuth2 refresh token has expired or is invalid. Please regenerate the refresh token.');
    } else if (lastError.message?.includes('unauthorized_client')) {
        throw new Error('OAuth2 client credentials are invalid. Please check CLIENT_ID and CLIENT_SECRET.');
    } else if (lastError.message?.includes('timed out')) {
        throw new Error(`OAuth2 service is not responding after ${MAX_RETRIES} attempts. Please check your network connection or try again later.`);
    } else if (lastError.code === 'ENOTFOUND' || lastError.code === 'ECONNREFUSED') {
        throw new Error('Network error: Unable to connect to Google OAuth2 service. Please check your internet connection.');
    }

    throw new Error(`Failed to get OAuth2 access token after ${MAX_RETRIES} attempts: ${lastError.message}`);
}

// Helper function to create SMTP transport with different configurations
function createSMTPTransport(accessToken: string, config: 'default' | 'port465' | 'direct') {
    const baseAuth = {
        type: 'OAuth2',
        user: process.env.MAIL_USER,
        clientId: process.env.CLIENT_ID,
        clientSecret: process.env.CLIENT_SECRET,
        refreshToken: process.env.REFRESH_TOKEN,
        accessToken: accessToken,
    };

    const baseOptions = {
        connectionTimeout: 60000, // 60 seconds
        greetingTimeout: 30000,   // 30 seconds
        socketTimeout: 60000,     // 60 seconds
        pool: false,
        maxConnections: 1,
        maxMessages: 1,
        debug: process.env.NODE_ENV === 'dev',
        logger: process.env.NODE_ENV === 'dev'
    };

    switch (config) {
        case 'default':
            return nodemailer.createTransport({
                service: 'gmail',
                auth: baseAuth,
                ...baseOptions
            } as any);

        case 'port465':
            return nodemailer.createTransport({
                host: 'smtp.gmail.com',
                port: 465,
                secure: true,
                auth: baseAuth,
                ...baseOptions
            } as any);

        case 'direct':
            return nodemailer.createTransport({
                host: 'smtp.gmail.com',
                port: 587,
                secure: false,
                auth: baseAuth,
                ...baseOptions,
                tls: {
                    rejectUnauthorized: false
                }
            } as any);

        default:
            throw new Error('Invalid SMTP configuration');
    }
}

async function sendEmail({ to, subject, html }: { to: string, subject: string, html: string }) {
    return new Promise(async (resolve, reject) => {
        try {
            console.log(`Sending email to: ${to}, subject: ${subject}`);

            // Get access token with timeout protection
            const accessToken = await getAccessTokenWithTimeout();

            const configurations: Array<'default' | 'port465' | 'direct'> = ['default', 'port465', 'direct'];
            let lastError: any;

            for (const config of configurations) {
                try {
                    console.log(`Trying SMTP configuration: ${config}`);
                    const transport = createSMTPTransport(accessToken, config);

                    // Test the connection before sending
                    console.log('Testing SMTP connection...');
                    await transport.verify();
                    console.log('SMTP connection verified successfully');

                    console.log('Sending email...');
                    await transport.sendMail({
                        to,
                        subject,
                        html,
                    });

                    console.log(`Email sent successfully to: ${to} using ${config} configuration`);
                    resolve('Email sent');
                    return;

                } catch (err: any) {
                    lastError = err;
                    console.error(`SMTP configuration ${config} failed:`, {
                        message: err.message,
                        code: err.code
                    });

                    // Don't retry for authentication errors
                    if (err.code === 'EAUTH' || err.message?.includes('authentication')) {
                        console.log('Authentication error detected, stopping retries');
                        break;
                    }
                }
            }

            // All configurations failed
            throw lastError || new Error('All SMTP configurations failed');

        } catch (err: any) {
            console.error(`Error sending email to ${to}:`, {
                message: err.message,
                code: err.code,
                stack: err.stack
            });
            reject(err)
        }
    })
}

export {
    sendEmail
}