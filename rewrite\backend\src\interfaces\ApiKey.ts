import mongoose from "mongoose";

export interface IApiKey {
    _id: mongoose.Types.ObjectId | string;
    api_key: string;
    description: string;
    email: string | null;
    allowed_endpoints: number[];
    is_deleted: boolean;
    is_revoked: boolean;
    requests: number;
    requests_endpoints: { [key: number]: number };
    jwt_token: string;
    allowed_vessels: string[];
    created_by: mongoose.Types.ObjectId;
    creation_timestamp: Date;
    last_used: Date | null;
}