import db from "../modules/db";
import mongoose from "mongoose";
import User from "./User";

const organizationSchema = new mongoose.Schema({
    name: { type: String, required: true },
    domain: { type: String, required: true, unique: true },
    is_internal: { type: Boolean, required: true, default: false },
    is_miscellaneous: { type: Boolean, required: true, default: false },
    created_by: { type: mongoose.Schema.Types.ObjectId, ref: User },
    creation_timestamp: { type: Date, required: true, default: () => new Date().toISOString() },
});

// organizationSchema.post("save", emitChangedEvent);

// organizationSchema.post("findOneAndDelete", emitChangedEvent);

// function emitChangedEvent(organization) {
//     ioEmitter.emit("notifyAll", { name: `organization/changed`, data: organization.toObject() });
// }

const Organization = db.qm.model("Organization", organizationSchema, "organizations");

export default Organization;
// module.exports.emitChangedEvent = process.env.NODE_ENV === "test" && emitChangedEvent;
