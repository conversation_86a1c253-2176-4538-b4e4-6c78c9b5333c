const { default: mongoose } = require("mongoose");
const db = require("../modules/db");
const { getSimplifiedCoords } = require("../utils/functions");

class VesselLocationService {
    async findByDateRange({ dateRange, vesselIds, projection }) {
        const start = new Date(dateRange[0]);
        const end = new Date(dateRange[1]);
        console.log("dateRange", dateRange);
        if (start.getUTCMonth() !== end.getUTCMonth()) throw new Error("Date range must be within the same month");

        const isoSplit = start.toISOString().split("-");
        const yearMonth = isoSplit[0] + "-" + isoSplit[1];

        console.log("querying collection", `${yearMonth}`);

        const query = {
            timestamp: { $gte: start, $lte: end },
            "metadata.onboardVesselId": { $in: vesselIds.map((id) => new mongoose.Types.ObjectId(id)) },
        };

        console.log("query", query);

        let cursor;

        const optimizedCollectionExists = (await db.locationsOptimized.db.listCollections({ name: `${yearMonth}` }).toArray()).length;
        if (optimizedCollectionExists) {
            cursor = db.locationsOptimized.collection(`${yearMonth}`).find(query, { projection });
        } else {
            cursor = db.locationsRaw.collection(`${yearMonth}`).find(query, { projection });
        }

        cursor.hint({ timestamp: 1, "metadata.onboardVesselId": 1 });
        cursor.batchSize(this.calculateOptimalBatchSize(dateRange, optimizedCollectionExists));

        const ts = new Date().getTime();

        let locations = [];

        for await (const doc of cursor) {
            locations.push(doc);
        }

        console.log(
            `time taken to query DB ${optimizedCollectionExists ? db.locationsOptimized.name : db.locationsRaw.name} and collection ${yearMonth}: ${new Date().getTime() - ts}ms`,
        );

        if (!optimizedCollectionExists) {
            console.log("optimizing ungrouped coords");
            console.log("prev locations.length", locations.length);
            locations = this.optimizeUngroupedCoords(locations);
            console.log("post locations.length", locations.length);
        }

        return locations;
    }

    calculateOptimalBatchSize(dateRange, optimizedCollectionExists) {
        let batchPerDay = 2000;

        if (optimizedCollectionExists) {
            batchPerDay = 1000;
        }

        const timeDiffDays = (new Date(dateRange[1]).getTime() - new Date(dateRange[0]).getTime()) / (1000 * 60 * 60 * 24);

        console.log("timeDiffDays", timeDiffDays);

        const batchSize = Math.floor(timeDiffDays * batchPerDay);

        console.log("batchSize", batchSize);

        return batchSize;
    }

    optimizeUngroupedCoords(locationsSorted) {
        const groupedLocations = locationsSorted.reduce((acc, loc) => {
            const vesselId = loc.metadata.onboardVesselId.toString();
            if (!acc[vesselId]) {
                acc[vesselId] = [];
            }
            acc[vesselId].push(loc);
            return acc;
        }, {});

        const optimizedGroupedLocations = Object.values(groupedLocations)
            .flatMap((locations) => getSimplifiedCoords(locations))
            .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

        return optimizedGroupedLocations;
    }
}

const vesselLocationService = new VesselLocationService();

module.exports = vesselLocationService;
