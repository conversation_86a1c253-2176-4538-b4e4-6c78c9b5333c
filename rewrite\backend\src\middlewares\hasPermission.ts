import { NextFunction, Request, Response } from "express";
import { permissions as permissionsObj } from "src/utils/permissions";

const hasPermission = (permissions: number[], req: Request, res: Response, next: NextFunction) => {
    if (req.user) {
        if (permissions.every(p_id => req.user.permissions.some(p => p.permission_id === p_id))) {
            return next();
        } else {
            return res.status(403).json({ message: 'You cannot access this resource' });
        }
    } else if (req.api_key) {
        // already authorized in auth middleware
        return next();
    } else {
        return res.status(403).json({ message: 'Login to access this resource' });
    }
}

export default hasPermission