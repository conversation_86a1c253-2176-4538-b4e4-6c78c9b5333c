const mongoose = require("mongoose");
const db = require("../modules/db");

const ArtifactSuggestionSchema = new mongoose.Schema({
    search: { type: String, required: true },
    click: { type: Number, default: 0 },
    impressions: { type: Number, default: 0 },
});

ArtifactSuggestionSchema.index({ search: 1 }, { unique: true });

const ArtifactSuggestion = db.qm.model("ArtifactSuggestion", ArtifactSuggestionSchema, "artifact_suggestions");

module.exports = ArtifactSuggestion;
